<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { NCard, NTag, NButton, NSpace } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  performanceData?: Api.Monitor.PerformanceData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  performanceData: null,
  loading: false
});

const router = useRouter();

// 响应时间趋势图
const { domRef: responseTimeRef, updateOptions: updateResponseTime } = useEcharts(() => ({
  title: {
    text: '响应时间趋势',
    left: 'center',
    top: '5%',
    fontSize: 14,
    fontWeight: 'bold'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '25%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLabel: {
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    name: '响应时间(ms)',
    nameTextStyle: {
      fontSize: 10
    },
    axisLabel: {
      fontSize: 10,
      formatter: '{value}ms'
    }
  },
  series: [{
    name: '响应时间',
    type: 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      opacity: 0.3,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: '#1890ff'
        }, {
          offset: 1, color: 'rgba(24, 144, 255, 0.1)'
        }]
      }
    },
    itemStyle: {
      color: '#1890ff'
    },
    data: []
  }]
}));

// 性能指标仪表盘
const { domRef: performanceGaugeRef, updateOptions: updatePerformanceGauge } = useEcharts(() => ({
  series: [{
    type: 'gauge',
    startAngle: 180,
    endAngle: 0,
    center: ['50%', '75%'],
    radius: '90%',
    min: 0,
    max: 5000,
    splitNumber: 5,
    axisLine: {
      lineStyle: {
        width: 6,
        color: [
          [0.2, '#52c41a'],
          [0.6, '#faad14'],
          [1, '#ff4d4f']
        ]
      }
    },
    pointer: {
      icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
      length: '12%',
      width: 20,
      offsetCenter: [0, '-60%'],
      itemStyle: {
        color: 'auto'
      }
    },
    axisTick: {
      length: 12,
      lineStyle: {
        color: 'auto',
        width: 2
      }
    },
    splitLine: {
      length: 20,
      lineStyle: {
        color: 'auto',
        width: 5
      }
    },
    axisLabel: {
      color: '#464646',
      fontSize: 10,
      distance: -50,
      rotate: 'tangential',
      formatter: function (value: number) {
        if (value === 0) return '0ms';
        if (value === 1000) return '1s';
        if (value === 2500) return '2.5s';
        if (value === 5000) return '5s';
        return '';
      }
    },
    title: {
      offsetCenter: [0, '-10%'],
      fontSize: 12,
      fontWeight: 'bold',
      color: '#464646'
    },
    detail: {
      fontSize: 16,
      fontWeight: 'bold',
      offsetCenter: [0, '-35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return Math.round(value) + 'ms';
      },
      color: 'auto'
    },
    data: [{
      value: 0,
      name: '平均响应时间'
    }]
  }]
}));

// 计算性能统计
const performanceStats = computed(() => {
  if (!props.performanceData) {
    return {
      avgResponseTime: 0,
      requestCount: 0,
      errorRate: 0,
      requestsPerMinute: 0
    };
  }

  const api = props.performanceData.api_performance;

  // 处理错误率格式：如果值小于等于1，认为是小数格式(0-1)，需要乘以100；否则认为已经是百分比格式(0-100)
  const rawErrorRate = api.error_rate || 0;
  const errorRate = rawErrorRate <= 1 ? rawErrorRate * 100 : rawErrorRate;

  return {
    avgResponseTime: api.avg_response_time * 1000, // 转换为毫秒
    requestCount: api.request_count,
    errorRate: Math.min(errorRate, 100), // 确保不超过100%
    requestsPerMinute: api.requests_per_minute
  };
});

// 响应时间状态
const responseTimeStatus = computed(() => {
  const time = performanceStats.value.avgResponseTime;
  if (time <= 500) return { type: 'success', color: '#52c41a', text: '优秀' };
  if (time <= 1000) return { type: 'warning', color: '#faad14', text: '良好' };
  if (time <= 3000) return { type: 'warning', color: '#faad14', text: '一般' };
  return { type: 'error', color: '#ff4d4f', text: '较慢' };
});

// 错误率状态
const errorRateStatus = computed(() => {
  const rate = performanceStats.value.errorRate;
  if (rate <= 1) return { type: 'success', color: '#52c41a', text: '正常' };
  if (rate <= 5) return { type: 'warning', color: '#faad14', text: '注意' };
  return { type: 'error', color: '#ff4d4f', text: '异常' };
});

// 生成模拟趋势数据
function generateTrendData() {
  const now = new Date();
  const data = [];
  const times = [];

  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60 * 60 * 1000);
    times.push(time.getHours() + ':00');

    // 生成模拟数据，基于当前响应时间添加随机波动
    const baseTime = performanceStats.value.avgResponseTime;
    const variation = (Math.random() - 0.5) * baseTime * 0.3;
    data.push(Math.max(50, baseTime + variation));
  }

  return { times, data };
}

// 监听数据变化，更新图表
watch(() => props.performanceData, () => {
  if (props.performanceData) {
    const stats = performanceStats.value;

    // 更新仪表盘
    updatePerformanceGauge(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [{
          value: stats.avgResponseTime,
          name: '平均响应时间'
        }]
      }]
    }));

    // 更新趋势图
    const { times, data } = generateTrendData();
    updateResponseTime(prev => ({
      ...prev,
      xAxis: {
        ...prev.xAxis,
        data: times
      },
      series: [{
        ...prev.series[0],
        data: data
      }]
    }));
  }
}, { immediate: true });

function goToPerformanceDetail() {
  router.push('/monitor/performance');
}
</script>

<template>
  <NCard class="enhanced-performance-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon icon="mdi:speedometer" class="text-2xl text-blue-500" />
          <span class="text-lg font-semibold">性能监控</span>
        </div>
        <NButton text @click="goToPerformanceDetail">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 响应时间仪表盘 -->
      <div class="chart-container">
        <div ref="performanceGaugeRef" class="performance-gauge"></div>
      </div>

      <!-- 响应时间趋势图 -->
      <div class="chart-container">
        <div ref="responseTimeRef" class="response-time-chart"></div>
      </div>
    </div>

    <!-- 性能指标卡片 -->
    <div class="mt-6">
      <NGrid :cols="2" :md-cols="4" :x-gap="16" :y-gap="16">
        <NGridItem>
          <div class="metric-card response-time">
            <div class="metric-icon">
              <Icon icon="mdi:timer-outline" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime.toFixed(0) }}ms</div>
              <div class="metric-label">平均响应时间</div>
              <NTag :type="responseTimeStatus.type" size="small" class="mt-1">
                {{ responseTimeStatus.text }}
              </NTag>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="metric-card request-count">
            <div class="metric-icon">
              <Icon icon="mdi:counter" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestCount.toLocaleString() }}</div>
              <div class="metric-label">总请求数</div>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="metric-card error-rate">
            <div class="metric-icon">
              <Icon icon="mdi:alert-circle-outline" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.errorRate.toFixed(2) }}%</div>
              <div class="metric-label">错误率</div>
              <NTag :type="errorRateStatus.type" size="small" class="mt-1">
                {{ errorRateStatus.text }}
              </NTag>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="metric-card requests-per-minute">
            <div class="metric-icon">
              <Icon icon="mdi:chart-line" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestsPerMinute.toFixed(1) }}</div>
              <div class="metric-label">每分钟请求</div>
            </div>
          </div>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 性能状态总览 -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
      <div class="flex justify-between items-center mb-3">
        <span class="text-sm font-medium text-gray-700">性能状态评估</span>
        <div class="flex gap-2">
          <NTag :type="responseTimeStatus.type" size="small">
            响应时间: {{ responseTimeStatus.text }}
          </NTag>
          <NTag :type="errorRateStatus.type" size="small">
            错误率: {{ errorRateStatus.text }}
          </NTag>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div>
          <div class="text-xs text-gray-500 mb-1">响应时间表现</div>
          <NProgress
            :percentage="Math.max(0, 100 - (performanceStats.avgResponseTime / 50))"
            :color="responseTimeStatus.color"
            :show-indicator="false"
            :height="6"
          />
        </div>
        <div>
          <div class="text-xs text-gray-500 mb-1">稳定性表现</div>
          <NProgress
            :percentage="Math.max(0, 100 - performanceStats.errorRate * 20)"
            :color="errorRateStatus.color"
            :show-indicator="false"
            :height="6"
          />
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToPerformanceDetail" type="primary" ghost>
          <template #icon>
            <Icon icon="mdi:chart-timeline-variant" />
          </template>
          查看性能详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.enhanced-performance-overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.enhanced-performance-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.performance-gauge,
.response-time-chart {
  width: 100%;
  height: 200px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  border: 1px solid #e2e8f0;
}

.metric-card:hover {
  transform: translateY(-3px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 不同卡片的悬停效果 */
.metric-card.response-time:hover {
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.25);
  border-left-color: #1e3a8a;
}

.metric-card.request-count:hover {
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.25);
  border-left-color: #047857;
}

.metric-card.error-rate:hover {
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.25);
  border-left-color: #b91c1c;
}

.metric-card.requests-per-minute:hover {
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.25);
  border-left-color: #6d28d9;
}

/* 响应时间卡片 - 深蓝科技渐变 */
.metric-card.response-time {
  border-left: 4px solid #1e40af;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
}

/* 总请求数卡片 - 翠绿活力渐变 */
.metric-card.request-count {
  border-left: 4px solid #059669;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 50%, #6ee7b7 100%);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.15);
}

/* 错误率卡片 - 暖红警示渐变 */
.metric-card.error-rate {
  border-left: 4px solid #dc2626;
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 50%, #f87171 100%);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

/* 每分钟请求卡片 - 紫色优雅渐变 */
.metric-card.requests-per-minute {
  border-left: 4px solid #7c3aed;
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 50%, #c4b5fd 100%);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.15);
}

.metric-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  color: #64748b;
}

/* 为不同卡片的图标添加特定颜色和效果 */
.metric-card.response-time .metric-icon {
  color: #1e40af;
  background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(30, 64, 175, 0.2) 100%);
  border: 1px solid rgba(30, 64, 175, 0.2);
}

.metric-card.request-count .metric-icon {
  color: #059669;
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.2) 100%);
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.metric-card.error-rate .metric-icon {
  color: #dc2626;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(220, 38, 38, 0.2) 100%);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.metric-card.requests-per-minute .metric-icon {
  color: #7c3aed;
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.1) 0%, rgba(124, 58, 237, 0.2) 100%);
  border: 1px solid rgba(124, 58, 237, 0.2);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: bold;
  line-height: 1;
  color: #1e293b;
}

.metric-label {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 2px;
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .performance-gauge,
  .response-time-chart {
    height: 150px;
  }
}

@media (max-width: 768px) {
  .metric-card {
    padding: 12px;
  }

  .metric-value {
    font-size: 1rem;
  }

  .metric-icon {
    width: 32px;
    height: 32px;
    font-size: 1.25rem;
  }
}
</style>
